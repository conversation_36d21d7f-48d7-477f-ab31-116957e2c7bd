import React from 'react';
import Chip from '../common/chips/Chip';

const GreekSymbolsTest: React.FC = () => {
  const greekTableContent = `**Table of Greek Symbols**

Here is a table of common Greek symbols used in mathematics and science, along with their names and LaTeX representations:

| Symbol | Name       | LaTeX Code  |
|--------|------------|-------------|
| \\( \\alpha \\) | Alpha      | \\alpha     |
| \\( \\beta \\)  | Beta       | \\beta      |
| \\( \\gamma \\) | Gamma      | \\gamma     |
| \\( \\delta \\) | Delta      | \\delta     |
| \\( \\epsilon \\) | Epsilon    | \\epsilon   |
| \\( \\lambda \\) | Lambda     | \\lambda    |
| \\( \\mu \\)    | Mu         | \\mu        |
| \\( \\sigma \\) | Sigma      | \\sigma     |
| \\( \\pi \\)    | Pi         | \\pi        |
| \\( \\theta \\) | Theta      | \\theta     |
| \\( \\phi \\)   | Phi        | \\phi       |
| \\( \\omega \\) | Omega      | \\omega     |

These symbols should render properly with LaTeX formatting.`;

  const testCases = [
    {
      id: 1,
      label: greekTableContent,
      description: 'Complete Greek symbols table with LaTeX formatting',
    },
    {
      id: 2,
      label: 'Simple test: Alpha Beta Gamma symbols should auto-convert',
      description: 'Auto-conversion test for Greek letter names',
    },
    {
      id: 3,
      label: 'Direct LaTeX: $\\alpha$ $\\beta$ $\\gamma$ $\\delta$ $\\epsilon$',
      description: 'Direct LaTeX syntax test',
    },
    {
      id: 4,
      label: 'Mixed content: The Alpha particle has energy $E = mc^2$ where Alpha = $\\alpha$',
      description: 'Mixed auto-conversion and direct LaTeX',
    },
    {
      id: 5,
      label: 'All Greek letters: Alpha Beta Gamma Delta Epsilon Zeta Eta Theta Iota Kappa Lambda Mu Nu Xi Omicron Pi Rho Sigma Tau Upsilon Phi Chi Psi Omega',
      description: 'Complete Greek alphabet auto-conversion test',
    },
  ];

  return (
    <div style={{ padding: '20px', maxWidth: '1000px', margin: '0 auto' }}>
      <h1>Greek Symbols Rendering Test</h1>
      <p>Testing Greek symbol rendering in chat messages with LaTeX support:</p>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        {testCases.map((testCase) => (
          <div key={testCase.id} style={{ 
            border: '1px solid #ddd', 
            borderRadius: '8px', 
            padding: '16px',
            backgroundColor: '#f9f9f9'
          }}>
            <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#666' }}>
              Test {testCase.id}: {testCase.description}
            </h3>
            <div style={{ marginBottom: '12px' }}>
              <Chip
                id={testCase.id}
                label={testCase.label}
                type="light"
                sender="bot"
                timestamp={new Date().toLocaleTimeString()}
                showExport={true}
                handleOnClick={() => console.log(`Clicked test ${testCase.id}`)}
              />
            </div>
          </div>
        ))}
      </div>

      <div style={{ marginTop: '32px', padding: '16px', backgroundColor: '#e8f4fd', borderRadius: '8px' }}>
        <h3>Expected Results:</h3>
        <ul>
          <li><strong>Test 1:</strong> Should show a properly formatted table with Greek symbols rendered in the Symbol column</li>
          <li><strong>Test 2:</strong> Should show "Simple test: α β γ symbols should auto-convert"</li>
          <li><strong>Test 3:</strong> Should show rendered Greek letters: α β γ δ ε</li>
          <li><strong>Test 4:</strong> Should show mixed content with both auto-converted and direct LaTeX symbols</li>
          <li><strong>Test 5:</strong> Should show the complete Greek alphabet with all letters rendered as symbols</li>
        </ul>
      </div>

      <div style={{ marginTop: '16px', padding: '16px', backgroundColor: '#d4edda', borderRadius: '8px' }}>
        <h3>✅ Fix Applied:</h3>
        <p>The Greek symbols should now render properly because:</p>
        <ol>
          <li>Enabled <code>enableGreekConversion</code> in MarkdownRenderer</li>
          <li>Enabled <code>enableLatexPreprocessing</code> for LaTeX support</li>
          <li>Enabled <code>enableCurrencyProtection</code> to avoid conflicts with dollar signs</li>
          <li>Properly configured remarkMath and rehypeKatex plugins</li>
        </ol>
      </div>

      <div style={{ marginTop: '16px', padding: '16px', backgroundColor: '#fff3cd', borderRadius: '8px' }}>
        <h3>How it works:</h3>
        <p>The system automatically converts Greek letter names to LaTeX symbols:</p>
        <ul>
          <li><code>Alpha</code> → <code>$\\alpha$</code> → α</li>
          <li><code>Beta</code> → <code>$\\beta$</code> → β</li>
          <li><code>Gamma</code> → <code>$\\gamma$</code> → γ</li>
          <li>And so on for all Greek letters...</li>
        </ul>
      </div>
    </div>
  );
};

export default GreekSymbolsTest;
